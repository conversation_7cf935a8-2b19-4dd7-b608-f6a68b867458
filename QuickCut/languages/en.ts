<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS>
<TS version="2.1" language="en" sourcelanguage="zh_CN">
<context>
    <name></name>
    <message>
        <location filename="../QuickCut.py" line="828"/>
        <source>复制视频流到mp4容器</source>
        <translation>Copy stream to mp4 container</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="839"/>
        <source>将输入文件打包到mkv格式容器</source>
        <translation>Package to mkv container</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="851"/>
        <source>转码到mp3格式</source>
        <translation>Transcode to mp3</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="862"/>
        <source>GIF (15fps 480p)</source>
        <translation>GIF (15fps, 480p)</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="875"/>
        <source>区域模糊</source>
        <translation>Boxblur</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="887"/>
        <source>视频两倍速</source>
        <translation>2x video</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="899"/>
        <source>音频两倍速</source>
        <translation>2x audio</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="911"/>
        <source>视频0.5倍速 + 光流法补帧到60帧</source>
        <translation>0.5x video + interpolate to 60 fps</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="923"/>
        <source>光流法补帧到60帧</source>
        <translation>Interpolate to 60 fps</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="935"/>
        <source>视频倒放</source>
        <translation>Revert video</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="947"/>
        <source>音频倒放</source>
        <translation>Revert audio</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="959"/>
        <source>设置画面比例</source>
        <translation>Set video ratio</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="971"/>
        <source>视频流时间戳偏移，用于同步音画</source>
        <translation>Timestamp offset</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="983"/>
        <source>从视频区间每秒提取n张照片</source>
        <translation>Extract n images per second</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="996"/>
        <source>截取指定数量的帧保存为图片</source>
        <translation>Extract n images</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1009"/>
        <source>一图流</source>
        <translation>One picture video</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1022"/>
        <source>裁切视频画面</source>
        <translation>Cut video frame</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1034"/>
        <source>视频旋转度数</source>
        <translation>Rotate video</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1046"/>
        <source>水平翻转画面</source>
        <translation>Flip video horizontally</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1058"/>
        <source>垂直翻转画面</source>
        <translation>Flip video vertically</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1070"/>
        <source>设定至指定分辨率，并且自动填充黑边</source>
        <translation>Set to specified resolution and fill black edge</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1082"/>
        <source>视频或音乐添加封面图片</source>
        <translation>Add a cover to video or music</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1094"/>
        <source>声音响度标准化</source>
        <translation>Sound normalization</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1106"/>
        <source>音量大小调节</source>
        <translation>Adjust sound volume</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1118"/>
        <source>静音第一个声道</source>
        <translation>Mute the first audio channel</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1130"/>
        <source>静音所有声道</source>
        <translation>Mute all audio channel</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1142"/>
        <source>交换左右声道</source>
        <translation>Exchange left and right channel</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1154"/>
        <source>两个音频流混合到一个文件</source>
        <translation>Mix two audio to one file</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1232"/>
        <source>删除预设</source>
        <translation>Delete Template</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1232"/>
        <source>将要删除“%s”预设，是否确认？</source>
        <translation>About to delete &quot;%s&quot; template, confirm? </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1241"/>
        <source>删除失败</source>
        <translation>Detetion fail</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1241"/>
        <source>还没有选择要删除的预设</source>
        <translation>You haven&apos;t choose a template.</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1282"/>
        <source>预设描述</source>
        <translation>Template Description</translation>
    </message>
</context>
<context>
    <name>AddApiDialog</name>
    <message>
        <location filename="../QuickCut.py" line="4100"/>
        <source>添加或更新 Api</source>
        <translation>Add or update API</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4106"/>
        <source>引擎名字：</source>
        <translation>Engine Name: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4108"/>
        <source>例如：阿里-中文</source>
        <translation>Example: Alibaba-English</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4111"/>
        <source>服务商：</source>
        <translation>Provider: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4121"/>
        <source>语言：</source>
        <translation>Language: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4150"/>
        <source>确定</source>
        <translation>OK</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4152"/>
        <source>取消</source>
        <translation>Cancel</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4191"/>
        <source>由 Api 的云端配置决定</source>
        <translation>Controled by API cloud config</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4272"/>
        <source>添加Api</source>
        <translation>Add API</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4272"/>
        <source>新Api添加失败，你可以把失败过程重新操作记录一遍，然后发给作者</source>
        <translation>Failed to add new API, you may record your process adn send it to the developer. </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4274"/>
        <source>覆盖Api</source>
        <translation>Overwrite API</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4274"/>
        <source>已经存在名字相同的Api，你可以选择换一个Api名称或者覆盖旧的Api。是否要覆盖？</source>
        <translation>An API with the same name already exists, you could overwrite it or change the new API name.
Do you want to overwrite?</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4287"/>
        <source>更新Api</source>
        <translation>Update API</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4284"/>
        <source>Api更新成功</source>
        <translation>API updated successfully! </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4287"/>
        <source>Api更新失败，你可以把失败过程重新操作记录一遍，然后发给作者</source>
        <translation>Failed to update API, you may record your operation and send it to the developer. </translation>
    </message>
</context>
<context>
    <name>AliTrans</name>
    <message>
        <location filename="../QuickCut.py" line="6169"/>
        <source>录音文件识别请求成功响应！
</source>
        <translation>The request of recognition was responded successfully! </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="6172"/>
        <source>录音文件识别请求失败！
</source>
        <translation>The request of recognition failed! </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="6198"/>
        <source>云端任务正在排队中，3 秒后重新查询
</source>
        <translation>The cloud task is queeing, query again 3 seconds later. </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="6200"/>
        <source>音频转文字中，3 秒后重新查询
</source>
        <translation>The cloud task is on work, query again 3 seconds later. </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="6212"/>
        <source>录音文件识别成功！
</source>
        <translation>The recognition task succeded! </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="6214"/>
        <source>录音文件识别失败！
</source>
        <translation>The recognition task failed! </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="6232"/>
        <source>上传 oss 目标路径：</source>
        <translation>OSS destinition of uploading: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="6235"/>
        <source>上传音频中
</source>
        <translation>Uploading audio</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="6237"/>
        <source>音频上传完毕，路径是：%s
</source>
        <translation>Audio uploaded, the url is: %s</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="6240"/>
        <source>正在识别中
</source>
        <translation>Recognizing</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="6245"/>
        <source>识别完成，现在删除 oss 上的音频文件：</source>
        <translation>Recognition complete, now deleting the audio file is oss: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="6281"/>
        <source>云端数据转字幕的过程中出错了，可能是没有识别到文字
</source>
        <translation>Audio to srt process went wrong, maybe it&apos;s because the recognition result has no words. </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="6305"/>
        <source>现在开始生成单声道、 16000Hz 的 wav 音频：%s 
</source>
        <translation>Now generation monochannel, 16000Hz wav file. </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="6319"/>
        <source>已删除 oss 音频文件
</source>
        <translation>OSS audio file deleted successfully! </translation>
    </message>
</context>
<context>
    <name>AutoEditThread</name>
    <message>
        <location filename="../QuickCut.py" line="5248"/>
        <source>删除临时文件夹 %s 失败</source>
        <translation>Failed to delete TEMP folder: %s</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="5253"/>
        <source>正在清除产生的临时文件夹：%s</source>
        <translation>Deleting the temp folder: %s</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="5283"/>
        <source>临时文件目录：%s 
</source>
        <translation>Temp folder path: %s</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="5292"/>
        <source>临时文件夹（%s）创建失败，请检查权限
</source>
        <translation>Failed to create temp folder: %s, please check out the permissions. </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="5293"/>
        <source>新建临时文件夹：%s 
</source>
        <translation>Create new temp folder: %s</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="5332"/>
        <source>转字幕出问题了，有可能是 oss 填写错误，或者语音引擎出错误，总之，请检查你的 api 和 KeyAccess 的权限</source>
        <translation>Failed to transcribe the subtitles, maybe it&apos;s the OSS or API info is not correct. Please check your API info. </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="5353"/>
        <source>视频帧率是: </source>
        <translation>The fps of video is: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="5354"/>
        <source>音频采样率是: </source>
        <translation>The audio samplerate is: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="5360"/>
        <source>

将所有视频帧提取到临时文件夹：%s

</source>
        <translation>Extracting all video frames to temp folder: %s</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="5376"/>
        <source>

分离出音频流：%s

</source>
        <translation>Extracting audio stream: %s</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="5405"/>
        <source>

正在分析音频

</source>
        <translation>Analysing audio</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="5442"/>
        <source>静音、响亮片段分析完成
</source>
        <translation>The anylization of silent and sounded clips completed.</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="5445"/>
        <source>开始根据字幕中的关键词处理片段
</source>
        <translation>Now start to process clips according to the keywords in subtitle file. </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="5466"/>
        <source>上一区间的结尾是: %s 
</source>
        <translation>The end of last region is: %s</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="5467"/>
        <source>这是区间是: %s 到 %s 
</source>
        <translation>This region is : %s to %s</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="5475"/>
        <source>这个 chunk (%s 到 %s) 在 cut 区间  %s 到 %s  左侧，下一个 chunk</source>
        <translation>This chunk(%s to %s) is at left the region(%s to %s), jump to next chunk</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="5503"/>
        <source>这个 chunk (%s 到 %s) 在 cut 区间  %s 到 %s  右侧，下一个区间</source>
        <translation>This chunk(%s to %s) is on the right side of region(%s to %s), jump to next region. </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="5486"/>
        <source>这个chunk 的右侧 %s 小于区间的终点  %s ，删掉</source>
        <translation>This chunk&apos;s right end(%s) is at the left of the current region end(%s), delete it. </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="5489"/>
        <source>这个chunk 的右侧 %s 大于区间的终点 %s ，把它的左侧 %s 改成本区间的终点 %s </source>
        <translation>This chunk(%s to %s)&apos;s right end is at the right of the current region(%s), change its left end to the end(%s) of this region. </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="5508"/>
        <source>这个区间 (%s 到 %s) 的右侧，在起点 %s 和终点 %s 之间，修改区间右侧为 %s </source>
        <translation>This region(%s to %s) is at the right side of the current chunk(%s to %s), change its right end to %s</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="5513"/>
        <source>这个区间 (%s 到 %s) 的左侧，在起点 %s 和终点 %s 之间，修改区间左侧为 %s </source>
        <translation>The left end of this region(%s to %s), is between the chunk(%s to %s), hange the left side of region to %s</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="5518"/>
        <source>这个区间 (%s 到 %s) 整个在起点 %s 和终点 %s 之间，删除 </source>
        <translation>This region(%s to %s) is between start %s and end %s, delete it. </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="5522"/>
        <source>这个区间 (%s 到 %s) 横跨了 %s 到 %s ，分成两个：从 %s 到 %s ，从 %s 到 %s  </source>
        <translation>This region (%s to %s) is from %s to %s, split into two: %s to %s, %s to %s </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="5531"/>
        <source>

开始根据分段信息处理音频
</source>
        <translation>Process audio by chunks info. </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="5607"/>
        <source>

现在开始合并音频片段


</source>
        <translation>Concat all the audio clips. </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="5622"/>
        <source>

现在开始合并音视频


</source>
        <translation>Combine audio and video. </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="5650"/>
        <source>


自动剪辑处理完成！


</source>
        <translation>Auto edit complete! </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="5652"/>
        <source>自动剪辑过程出错了，可能是因为启用了在线语音识别引擎，但是填写的 oss 和 api 有误，如果是其它原因，你可以将问题出现过程记录下，在帮助页面加入 QQ 群向作者反馈。</source>
        <translation>Auto edit process failed. You may record the process and send it to the developer. </translation>
    </message>
</context>
<context>
    <name>CapsWriterTab</name>
    <message>
        <location filename="../QuickCut.py" line="3592"/>
        <source>字幕语音 API：</source>
        <translation>Voice to Subtitle Engine: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3610"/>
        <source>停用 CapsWirter 语音输入</source>
        <translation>Disable CapsWrite Speech-To-Text service</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3611"/>
        <source>启用 CapsWirter 语音输入</source>
        <translation>Enable CapsWrite Speech-To-Text service</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3633"/>
        <source>选择阿里云 api 的引擎，启用 CapsWriter 语音输入后，只要在任意界面长按大写大写锁定键（Caps Lk）超过 0.3 秒，就会开始进行语音识别，说几句话，再松开大写锁定键，请别结果就会自动输入。你可以在这个输入框试试效果</source>
        <translation>Choose an Alibaba SpeechToText engine, enable CapsWriter, then in any interface, as long as you press Caps Lock more than 0.3 seconds, it will start recording, after releasing the Caps Lock, the recognition result will be typed immediately. You can try it in this text edit box. </translation>
    </message>
</context>
<context>
    <name>CapsWriterThread</name>
    <message>
        <location filename="../QuickCut.py" line="5799"/>
        <source>
初始化完成，现在可以将本工具最小化，在需要输入的界面，按住 CapsLock 键 0.3 秒后开始说话，松开 CapsLock 键后识别结果会自动输入
</source>
        <translation>Initiation complete, from now on, you can long press Caps Lock key more than 0.3 seconds and then speak, when the key is released, the words will be input automaticlly. </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="5802"/>
        <source>{}:按住 CapsLock 键 0.3 秒后开始说话...</source>
        <translation>{}:long press Caps Lock over 0.3s and then speak...</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="5942"/>
        <source>
{}:在听了，说完了请松开 CapsLock 键...</source>
        <translation>{}:Listning... After speaking, release the CapsLock key.</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="5956"/>
        <source>
{}:按住 CapsLock 键 0.3 秒后开始说话...</source>
        <translation>{}:long press Caps Lock over 0.3s and then speak...</translation>
    </message>
</context>
<context>
    <name>ClickableEndTimeLable</name>
    <message>
        <location filename="../QuickCut.py" line="1518"/>
        <source>截取时长：</source>
        <translation>Cut Duration: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1508"/>
        <source>点击交换“截取时长”和“截止时刻”</source>
        <translation>Click to exchange &quot;Cut Duration&quot; and &quot;End time&quot;.</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1516"/>
        <source>截止时刻：</source>
        <translation>End Time:</translation>
    </message>
</context>
<context>
    <name>ClickableResolutionTimesLable</name>
    <message>
        <location filename="../QuickCut.py" line="1527"/>
        <source>点击交换横纵分辨率</source>
        <translation>Click to exchange resolution values</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1531"/>
        <source>点击交换横竖分辨率</source>
        <translation>Click to exchange resolution values. </translation>
    </message>
</context>
<context>
    <name>CommandThread</name>
    <message>
        <location filename="../QuickCut.py" line="4782"/>
        <source>开始执行命令
</source>
        <translation>Command started. </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4792"/>
        <source>命令运行出错了，估计是你的 you-get、youtube-dl 没有安装上。快去看下视频教程的下载视频这一节吧，里面有安装 you-get 和 youtube-dl 的命令</source>
        <translation>Command went wrong. Please go checkout video tutorial in Help tab. </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4795"/>
        <source>帧数</source>
        <translation>frame</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4795"/>
        <source> 大小</source>
        <translation>size</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4795"/>
        <source> 时间</source>
        <translation>time</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4795"/>
        <source> 比特率</source>
        <translation>bitrate</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4795"/>
        <source> 速度</source>
        <translation>speed</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4798"/>
        <source>出错了，本次运行的命令是：

%s

你可以将上面这行命令复制到 cmd 窗口运行下，看看报什么错，如果自己解决不了，把那个报错信息发给开发者</source>
        <translation>Some thing went wrong, the command runs this time is: %s , you can run it in your shell to check out the error information. If it can&apos;t be resolved, you may record it and send it to the developer. </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4799"/>
        <source>
命令执行完毕
</source>
        <translation>Command complete. </translation>
    </message>
</context>
<context>
    <name>ConfigTab</name>
    <message>
        <location filename="../QuickCut.py" line="3761"/>
        <source>OSS对象存储设置：</source>
        <translation>OSS config:</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3765"/>
        <source>阿里OSS</source>
        <translation>Alibaba OSS</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3766"/>
        <source>腾讯OSS</source>
        <translation>Tencent OSS</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3785"/>
        <source>保存OSS配置</source>
        <translation>Save OSS config</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3787"/>
        <source>取消</source>
        <translation>Cancel</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3805"/>
        <source>语音 Api：</source>
        <translation>SpeechToText API: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3817"/>
        <source>引擎名称</source>
        <translation>Engine Name</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3819"/>
        <source>服务商</source>
        <translation>Provider</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3823"/>
        <source>语言</source>
        <translation>Language</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3883"/>
        <source>点击关闭按钮时隐藏到托盘</source>
        <translation>Hide to tray when close. </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3957"/>
        <source>重置 FFmpeg 预设</source>
        <translation>Reset FFmpeg Tab template</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3892"/>
        <source>语言：</source>
        <translation>Language: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3913"/>
        <source>打开 Python 下载页面</source>
        <translation>Open Python download page</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3914"/>
        <source>打开 FFmpeg 下载页面</source>
        <translation>Open FFmpeg download page</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3915"/>
        <source>安装 you-get 和 youtube-dl</source>
        <translation>Install you-get and youtube-dl</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3948"/>
        <source>将要重置 FFmpeg Tab 的预设列表，是否确认？</source>
        <translation>About to reset FFmpeg Tab template, confirm? </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3955"/>
        <source>重置 FFmpeg 预设成功</source>
        <translation>Reset FFmpeg Tab template succeed! </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3957"/>
        <source>重置 FFmpeg 预设失败</source>
        <translation>Reset FFmpeg Tab template failed! </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4003"/>
        <source>更改语言</source>
        <translation>Change Language</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4003"/>
        <source>更改的语言会在重启软件后生效</source>
        <translation>The language will take effect after relaunching the software. </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4055"/>
        <source>删除 Api</source>
        <translation>Delete API</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4055"/>
        <source>将要删除选中的 Api，是否确认？</source>
        <translation>About to delete selected API, confirm? </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4061"/>
        <source>删除失败</source>
        <translation>Detetion fail</translation>
    </message>
</context>
<context>
    <name>Console</name>
    <message>
        <location filename="../QuickCut.py" line="4550"/>
        <source>命令运行输出窗口</source>
        <translation>Command Line Output Window</translation>
    </message>
</context>
<context>
    <name>CutTimeEdit</name>
    <message>
        <location filename="../QuickCut.py" line="1569"/>
        <source>例如 “00:05.00”、“23.189”、“12:03:45”的形式都是有效的，注意冒号是英文冒号</source>
        <translation>Such as 00:05.00, 23.189, 12:03:45 are all valid.</translation>
    </message>
</context>
<context>
    <name>DownLoadVideoTab</name>
    <message>
        <location filename="../QuickCut.py" line="2370"/>
        <source>使用 Annie 下载视频：</source>
        <translation>Download videos using Annie: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="2555"/>
        <source>视频链接：</source>
        <translation>Video link: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="2557"/>
        <source>保存路径：</source>
        <translation>Save path: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="2472"/>
        <source>下载格式(流id)：</source>
        <translation>Stream Id: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="2572"/>
        <source>不填则默认下载最高画质</source>
        <translation>Blank means the default highest quality</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="2579"/>
        <source>默认不用填</source>
        <translation>Optional</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="2580"/>
        <source>选择文件</source>
        <translation>Choose File</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="2583"/>
        <source>代理：</source>
        <translation>Proxy: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="2490"/>
        <source>下载视频列表</source>
        <translation>Download the video list</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="2492"/>
        <source>列出流id</source>
        <translation>List Stream Id</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="2591"/>
        <source>开始下载视频</source>
        <translation>Start</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="2461"/>
        <source>使用 You-Get 下载视频：</source>
        <translation>Download videos using You-Get: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="2552"/>
        <source>使用 Youtube-dl 下载视频：</source>
        <translation>Download Videos using YouTube-dl</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="2563"/>
        <source>文件命名格式：</source>
        <translation>File name format: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="2566"/>
        <source>不填则使用默认下载名</source>
        <translation>Blank means the default filename</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="2570"/>
        <source>格式id：</source>
        <translation>Format id: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="2575"/>
        <source>只下载字幕</source>
        <translation>Only download subtitles</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="2588"/>
        <source>列出格式id</source>
        <translation>List Format Id</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="2651"/>
        <source>打开文件</source>
        <translation>Open File</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="2651"/>
        <source>所有文件(*)</source>
        <translation>All Files(*)</translation>
    </message>
</context>
<context>
    <name>DurationSplitVideoThread</name>
    <message>
        <location filename="../QuickCut.py" line="5083"/>
        <source>创建输出文件夹失败，可能是已经创建上了
</source>
        <translation>Failed to create output folder, maybe it already exists. </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="5088"/>
        <source>总共要处理的时长：%s 秒      导出的每个片段时长：%s 秒 
</source>
        <translation>Total time length to process: %s seconds           Time length of each exported clip: %s seconds</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="5093"/>
        <source>总共有 %s 个片段要导出，现在导出第 %s 个……
</source>
        <translation>%s clips need to be exported, now exporting number %s ......</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="5111"/>
        <source>导出完成
</source>
        <translation>Export complete</translation>
    </message>
</context>
<context>
    <name>FFmpegAutoEditTab</name>
    <message>
        <location filename="../QuickCut.py" line="2986"/>
        <source>输入文件</source>
        <translation>Input File</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="2987"/>
        <source>输出路径</source>
        <translation>Output Path</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="2991"/>
        <source>选择文件</source>
        <translation>Choose File</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="2993"/>
        <source>选择保存位置</source>
        <translation>Choose Save Path</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3010"/>
        <source>安静片段倍速：</source>
        <translation>Quiet Clips Speed: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3015"/>
        <source>响亮片段倍速：</source>
        <translation>Sounded Clips Speed: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3020"/>
        <source>片段间缓冲帧数：</source>
        <translation>Margin of Clips: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3024"/>
        <source>声音检测相对阈值：</source>
        <translation>Silence Threshold: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3033"/>
        <source>提取帧选项：</source>
        <translation>Extract Option:</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3038"/>
        <source>这里可以选择硬件加速编码器、调整提取帧的质量</source>
        <translation>Here you can select hardware accelerating codecs, adjust frame quality</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3041"/>
        <source>提取帧质量：</source>
        <translation>Frame Quality: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3047"/>
        <source>输出文件选项：</source>
        <translation>Output Option:</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3052"/>
        <source>在这里可以选择对应你设备的硬件加速编码器，Intel 对应 qsv，AMD 对应 amf，Nvidia 对应 nvenc, 苹果电脑对应 videotoolbox</source>
        <translation></translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3065"/>
        <source>生成自动字幕并依据字幕中的关键句自动剪辑</source>
        <translation>Auto generate subtitles and auto edit video by keywords in subtitles.</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3068"/>
        <source>字幕语音 API：</source>
        <translation>Voice to Subtitle Engine: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3079"/>
        <source>剪去片段关键句：</source>
        <translation>Keywords indicates cut:</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3082"/>
        <source>切掉</source>
        <translation>delete</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3083"/>
        <source>保留片段关键句：</source>
        <translation>Keywords indicates save: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3086"/>
        <source>保留</source>
        <translation>save</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3136"/>
        <source>运行</source>
        <translation>Run</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3154"/>
        <source>打开文件</source>
        <translation>Open File</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3162"/>
        <source>所有文件(*)</source>
        <translation>All Files(*)</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3162"/>
        <source>设置输出保存的文件名</source>
        <translation>Set the output file path</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3162"/>
        <source>输出视频.mp4</source>
        <translation>Output.mp4</translation>
    </message>
</context>
<context>
    <name>FFmpegAutoSrtTab</name>
    <message>
        <location filename="../QuickCut.py" line="3244"/>
        <source>通过录音文件识别引擎转字幕：</source>
        <translation>Transcribe audio to srt subtitle by SpeechToText engine: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3306"/>
        <source>输入文件：</source>
        <translation>Input file: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3309"/>
        <source>选择文件</source>
        <translation>Choose File</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3312"/>
        <source>字幕输出文件：</source>
        <translation>Subtitle output file: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3257"/>
        <source>字幕语音 API：</source>
        <translation>Voice to Subtitle Engine: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3267"/>
        <source>开始运行</source>
        <translation>Start to run</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3304"/>
        <source>通过语音输入法转字幕：</source>
        <translation>Transcribe audio to srt subtitle by VoiceInputMethod app: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3317"/>
        <source>可选截取片段：</source>
        <translation>Choose one clip</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3319"/>
        <source>起始时间：</source>
        <translation>Start time: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3322"/>
        <source>结束时间：</source>
        <translation>End time: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3338"/>
        <source>语音输入快捷键：</source>
        <translation>VoiceInputMethod shortcut: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3345"/>
        <source>片段最短时间：</source>
        <translation>Min clip duration: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3351"/>
        <source>片段最长时间：</source>
        <translation>Max clip duration:</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3356"/>
        <source>段内静音最长时间：</source>
        <translation>Max silence duration in clip: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3362"/>
        <source>声音能量阈值：</source>
        <translation>Voice energy threshold: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3364"/>
        <source> 它是用 log10 dot(x, x) / |x| 计算出的能量的 log 值</source>
        <translation>It is the energy computed by log10 dot(x, x) / |x| </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3368"/>
        <source>输入法休息时间：</source>
        <translation>Rest time after audio: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3370"/>
        <source>每次输入完需要休息一下，否则在文字出来后很快再按下快捷键，语音输入法有可能响应不过来</source>
        <translation>Each time the audio finished, wait some time, to let VoiceInputMethod app give out result. </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3390"/>
        <source>查看帮助</source>
        <translation>Checkout Help</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3391"/>
        <source>开始半自动运行</source>
        <translation>Run semi-automaticlly</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3392"/>
        <source>开始全自动运行</source>
        <translation>Run automaticlly</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3483"/>
        <source>打开文件</source>
        <translation>Open File</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3483"/>
        <source>所有文件(*)</source>
        <translation>All Files(*)</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3499"/>
        <source>https://www.bilibili.com/video/BV1wT4y177kD/</source>
        <translation></translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3552"/>
        <source>输入文件有误</source>
        <translation>Input error</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="3552"/>
        <source>输入文件有误，请检查输入文件路径</source>
        <translation>Input file is not a valid file, please check out the input path. </translation>
    </message>
</context>
<context>
    <name>FFmpegConcatTab</name>
    <message>
        <location filename="../QuickCut.py" line="2156"/>
        <source>点击列表右下边的加号添加要合并的视频片段：</source>
        <translation>Click &quot;+&quot; button to add clips you want to concat: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="2172"/>
        <source>倒序</source>
        <translation>Revert</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="2187"/>
        <source>输出：</source>
        <translation>Output: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="2294"/>
        <source>选择保存位置</source>
        <translation>Choose Save Path</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="2198"/>
        <source>concat格式衔接，不重新解码、编码（快、无损、要求格式一致）</source>
        <translation>concat format, no decode and reencode</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="2199"/>
        <source>先转成 ts 格式，再衔接，要解码、编码（用于合并不同格式）</source>
        <translation>Transcode to ts then concat</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="2200"/>
        <source>concat滤镜衔接（视频为Stream0），要解码、编码</source>
        <translation>concat filter, stram 0 is video</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="2201"/>
        <source>concat滤镜衔接（音频为Stream0），要解码、编码</source>
        <translation>concat filter, stram 0 is audio</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="2209"/>
        <source>这里是自动生成的总命令</source>
        <translation>Here is the auto-generated command</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="2210"/>
        <source>运行</source>
        <translation>Run</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="2249"/>
        <source>清空列表</source>
        <translation>Clear list</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="2249"/>
        <source>是否确认清空列表？</source>
        <translation>Confirm to clear list? </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="2277"/>
        <source>添加音视频文件</source>
        <translation>Add media file</translation>
    </message>
</context>
<context>
    <name>FFmpegMainTab</name>
    <message>
        <location filename="../QuickCut.py" line="210"/>
        <source>输入1路径：</source>
        <translation>Input 1 path: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="214"/>
        <source>这里输入要处理的视频、音频文件</source>
        <translation>Drop in video, audio files</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="266"/>
        <source>选择文件</source>
        <translation>Choose File</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="274"/>
        <source>截取片段</source>
        <translation>Cut clip</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="277"/>
        <source>起始时间：</source>
        <translation>Start time: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="248"/>
        <source>输入1选项：</source>
        <translation>Input 1 option: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="261"/>
        <source>输入2路径：</source>
        <translation>Input 2 path: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="264"/>
        <source>输入2是选填的，只有涉及同时处理两个文件的操作才需要输入2</source>
        <translation>Input two is optional. Only requires when needed. </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="296"/>
        <source>输入2选项：</source>
        <translation>Input 2 option: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="316"/>
        <source>输出：</source>
        <translation>Output: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="318"/>
        <source>文件名填什么后缀，就会输出什么格式</source>
        <translation>Output will be in the format you set here. </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="319"/>
        <source>这里填写输出文件保存路径</source>
        <translation>Here you set output path. </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="321"/>
        <source>选择保存位置</source>
        <translation>Choose Save Path</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="329"/>
        <source>新分辨率</source>
        <translation>New resolution</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="340"/>
        <source>分辨率预设</source>
        <translation>Resolution Template</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="353"/>
        <source>输出选项：</source>
        <translation>Output option: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="381"/>
        <source>选择预设：</source>
        <translation>Choose template</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="391"/>
        <source>查看该预设帮助</source>
        <translation>Open help of this template</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="414"/>
        <source>这里是自动生成的总命令</source>
        <translation>Here is the auto-generated command</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="417"/>
        <source>运行</source>
        <translation>Run</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="608"/>
        <source>覆盖确认</source>
        <translation>Overwrite confirm</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="608"/>
        <source>输出路径对应的文件已存在，是否要覆盖？</source>
        <translation>The file of output alreads exists, confirm overwrite? </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="642"/>
        <source>不使用预设</source>
        <translation>Default preset</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="653"/>
        <source>H264压制</source>
        <translation>H264 compress</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="666"/>
        <source>H264压制 Intel 硬件加速</source>
        <translation>H264 compress(Intel accelerate)</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="679"/>
        <source>H264压制 AMD 硬件加速</source>
        <translation>H264 compress(AMD accelerate)</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="692"/>
        <source>H264压制 Nvidia 硬件加速</source>
        <translation>H264 compress(Nvidia accelerate)</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="705"/>
        <source>H264压制 Mac 硬件加速</source>
        <translation>H264 compress(Mac accelerate)</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="718"/>
        <source>H265压制</source>
        <translation>H265 compress</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="731"/>
        <source>H265压制 Intel 硬件加速</source>
        <translation>H265 compress(Intel accelerate)</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="744"/>
        <source>H265压制 AMD 硬件加速</source>
        <translation>H265 compress(AMD accelerate)</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="757"/>
        <source>H265压制 Nvidia 硬件加速</source>
        <translation>H265 compress(Nvidia accelerate)</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="770"/>
        <source>H265压制 Mac 硬件加速</source>
        <translation>H265 compress(Mac accelerate)</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="784"/>
        <source>H264压制目标比特率6000k</source>
        <translation>H264 compress target 6000 bit/s</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="797"/>
        <source>H264 二压 目标比特率2000k</source>
        <translation>H264 compress 2 pass target 2000 bit/s</translation>
    </message>
</context>
<context>
    <name>FFmpegSplitVideoTab</name>
    <message>
        <location filename="../QuickCut.py" line="1608"/>
        <source>输出文件选项(默认可为空，但可选硬件加速)：</source>
        <translation>Output option(hardware acceleration codec):</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1612"/>
        <source>在这里可以选择对应你设备的硬件加速编码器，Intel 对应 qsv，AMD 对应 amf，Nvidia 对应 nvenc, 苹果电脑对应 videotoolbox</source>
        <translation></translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1636"/>
        <source>对字幕中的每一句剪出对应的视频片段：</source>
        <translation>Split video clips by using subtitle file: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1639"/>
        <source>输入视频：</source>
        <translation>Input Video: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1862"/>
        <source>选择文件</source>
        <translation>Choose File</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1645"/>
        <source>输入字幕：</source>
        <translation>Input subtitle: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1647"/>
        <source>支持 srt、ass 字幕，或者内置字幕的 mkv</source>
        <translation>Support srt, ass, or mkv contains subtitle stream</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1864"/>
        <source>输出文件夹：</source>
        <translation>Output folder: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1876"/>
        <source>指定时间段</source>
        <translation>Cut clip</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1878"/>
        <source>起始时刻：</source>
        <translation>Start time: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1881"/>
        <source>截止时刻：</source>
        <translation>End Time:</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1676"/>
        <source>字幕时间偏移：</source>
        <translation>Subtitle timeline offset: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1684"/>
        <source>同时导出分段srt字幕</source>
        <translation>Split srt file</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1687"/>
        <source>每多少句剪为一段：</source>
        <translation>How many subs in one clip:</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1890"/>
        <source>运行</source>
        <translation>Run</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1766"/>
        <source>根据指定时长分割片段：</source>
        <translation>Split clips by specifized size: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1860"/>
        <source>输入路径：</source>
        <translation>Input path: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1779"/>
        <source>片段时长：</source>
        <translation>Clip duration: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1858"/>
        <source>根据指定大小分割片段：</source>
        <translation>Split clips by specifized size: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1868"/>
        <source>片段大小(MB)：</source>
        <translation>Clip size(MB):</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="2019"/>
        <source>打开文件</source>
        <translation>Open File</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="2019"/>
        <source>所有文件(*)</source>
        <translation>All Files(*)</translation>
    </message>
</context>
<context>
    <name>FileListWidget</name>
    <message>
        <location filename="../QuickCut.py" line="4378"/>
        <source>双击列表项可以清空文件列表</source>
        <translation>Double list items can clear the list</translation>
    </message>
</context>
<context>
    <name>FileTranscribeAutoSrtThread</name>
    <message>
        <location filename="../QuickCut.py" line="5681"/>
        <source>API 填写有误，请核实。无法继续转字幕，任务取消。</source>
        <translation>API info is not valid, please check. </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="5707"/>
        <source>转字幕出问题了，有可能是 oss 填写错误，或者语音引擎出错误，总之，请检查你的 api 和 KeyAccess 的权限

这次用到的 oss AccessKeyId 是：%s,       
这次用到的 oss AccessKeySecret 是：%s

这次用到的语音引擎 AppKey 是：%s，     
这次用到的语音引擎 AccessKeyId 是：%s，     
这次用到的语音引擎 AccessKeySecret 是：%s，    </source>
        <translation>Transcribe process went wrong. Your API maybe invalid. 

OSS AccessKeyId: %s
OSS AccessKeySecret: %s

SpeechToText engine AppKey:%s
SpeechToText engine AccessKeyId:%s
SpeechToText engine AccessKeySecret:%s</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="5710"/>
        <source>

转字幕完成

</source>
        <translation>Transcribe complete! </translation>
    </message>
</context>
<context>
    <name>HelpTab</name>
    <message>
        <location filename="../QuickCut.py" line="4319"/>
        <source>打开帮助文档</source>
        <translation>Open Help Document</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4320"/>
        <source>查看作者的 FFmpeg 笔记</source>
        <translation>Check out the developer&apos;s FFmpeg note</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4321"/>
        <source>查看视频教程</source>
        <translation>Watch Video Tutorial</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4322"/>
        <source>当前版本是 %s，到 Gitee 检查新版本</source>
        <translation>The current version is %s, goto Gitee for new version</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4323"/>
        <source>当前版本是 %s，到 Github 检查新版本</source>
        <translation>The current version is %s, goto Github for new version</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4324"/>
        <source>加入 QQ 群</source>
        <translation>Join QQ Group</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4325"/>
        <source>打赏作者</source>
        <translation>Sponsor the Developer</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4360"/>
        <source>./README.html</source>
        <translation>./README_en.html</translation>
    </message>
</context>
<context>
    <name>MainWindow</name>
    <message>
        <location filename="../QuickCut.py" line="104"/>
        <source>FFmpeg</source>
        <translation>FFmpeg</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="106"/>
        <source>分割视频</source>
        <translation>Split</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="108"/>
        <source>合并片段</source>
        <translation>Concat</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="115"/>
        <source>下载视频</source>
        <translation>Download</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="117"/>
        <source>自动剪辑</source>
        <translation>AutoEdit</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="118"/>
        <source>自动字幕</source>
        <translation>AutoSrt</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="119"/>
        <source>语音输入</source>
        <translation>VoiceInput</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="120"/>
        <source>设置</source>
        <translation>Settings</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="122"/>
        <source>帮助</source>
        <translation>Help</translation>
    </message>
</context>
<context>
    <name>MyCallback</name>
    <message>
        <location filename="../QuickCut.py" line="5829"/>
        <source>任务信息: task_id: %s, result: %s</source>
        <translation>Task_Id: %s, Result: %s</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="5834"/>
        <source>结果: %s</source>
        <translation>Result: %s</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="5846"/>
        <source>识别任务失败: %s</source>
        <translation>Task failed: %s</translation>
    </message>
</context>
<context>
    <name>ResolutionDialog</name>
    <message>
        <location filename="../QuickCut.py" line="1547"/>
        <source>选择分辨率预设</source>
        <translation>Resolution Preset</translation>
    </message>
</context>
<context>
    <name>ResolutionEdit</name>
    <message>
        <location filename="../QuickCut.py" line="1581"/>
        <source>负数表示自适应。例如，“ 720 × -2 ” 表示横轴分辨率为 720，纵轴分辨率为自适应且能够整除 -2</source>
        <translation>Negative number means adapt.720 × -2 indicates horizontal 720, vertical automaticlly adapt to the multiple of 2</translation>
    </message>
</context>
<context>
    <name>SetupPresetItemDialog</name>
    <message>
        <location filename="../QuickCut.py" line="1304"/>
        <source>添加或更新预设</source>
        <translation>Add or update template</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1309"/>
        <source>预设名称：</source>
        <translation>Template name: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1315"/>
        <source>输入1选项：</source>
        <translation>Input 1 option: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1320"/>
        <source>输入2选项：</source>
        <translation>Input 2 option: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1327"/>
        <source>输出后缀名：</source>
        <translation>Output extention: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1331"/>
        <source>输出选项：</source>
        <translation>Output option: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1337"/>
        <source>额外代码：</source>
        <translation>Extrad code: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1340"/>
        <source>这里是用于实现一些比较复杂的预设的，普通用户不用管这个框</source>
        <translation>Here is meant for some complex template, normal users don&apos;t need to fill this. </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1344"/>
        <source>描述：</source>
        <translation>Description: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1349"/>
        <source>确定</source>
        <translation>OK</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1351"/>
        <source>取消</source>
        <translation>Cancel</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1475"/>
        <source>添加预设</source>
        <translation>Add template</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1472"/>
        <source>新预设添加成功</source>
        <translation>Add template succeed! </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1475"/>
        <source>新预设添加失败，你可以把失败过程重新操作记录一遍，然后发给作者</source>
        <translation>Add template failed, you could record the process and send it to the developer. </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1477"/>
        <source>覆盖预设</source>
        <translation>Overwrite Template</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1477"/>
        <source>已经存在名字相同的预设，你可以选择换一个预设名字或者覆盖旧的预设。是否要覆盖？</source>
        <translation>A template with same name already exists, you may overwrite it or set a new name. Confirm overwrite? </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1492"/>
        <source>更新预设</source>
        <translation>Update Template</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1489"/>
        <source>预设更新成功</source>
        <translation>Template update succeed! </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="1492"/>
        <source>预设更新失败，你可以把失败过程重新操作记录一遍，然后发给作者</source>
        <translation>Template update failed, you could record the process and send it to the developer. </translation>
    </message>
</context>
<context>
    <name>SizeSplitVideoThread</name>
    <message>
        <location filename="../QuickCut.py" line="5171"/>
        <source>创建输出文件夹失败，可能是已经创建上了
</source>
        <translation>Failed to create output folder, maybe it already exists. </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="5175"/>
        <source>总共要处理的时长：%s 秒      导出的每个片段大小：%sMB 
</source>
        <translation>Total time of the input: %s, each clip should at the size of: %sMB. </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="5176"/>
        <source>需要知晓的是：最后导出的视频体积一般会略微超过您预设的大小，比如你设置每个片段为 20MB，实际导出的片段可能会达到 21MB 左右。
</source>
        <translation>Note: the final size may slightly bigger than the size you set. </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="5192"/>
        <source>






还有 %s 秒时长的片段要导出，总共已经导出 %s 秒的视频，目前正在导出的是第 %s 个片段……
</source>
        <translation>%s seconds remains to export. %s seconds have already been exported. Number %s is being exporting. </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="5203"/>
        <source>导出完成。
</source>
        <translation>Export finished. </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="5204"/>
        <source>应导出 %s 秒，实际导出 %s 秒。
</source>
        <translation>%s seconds should be exported. %s seconds was actually exported. </translation>
    </message>
</context>
<context>
    <name>SponsorDialog</name>
    <message>
        <location filename="../QuickCut.py" line="4412"/>
        <source>打赏作者</source>
        <translation>Sponsor the Developer</translation>
    </message>
</context>
<context>
    <name>SubtitleSplitVideoThread</name>
    <message>
        <location filename="../QuickCut.py" line="4898"/>
        <source>字幕是ass格式，先转换成srt格式
</source>
        <translation>The subtitle format is ass, transform to srt first. </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4929"/>
        <source>格式转换完成
</source>
        <translation>Format transformation finished. </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4946"/>
        <source>删除生成的srt字幕失败</source>
        <translation>Failed to delete generated srt subtitle. </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4923"/>
        <source>字幕是 mkv 格式，先转换成srt格式
</source>
        <translation>The subtitle format is mkv, transform to srt first. </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4953"/>
        <source>字幕格式只支持 srt 和 ass，以及带内置字幕的 mkv 文件，暂不支持您所选的字幕。

如果您的字幕输入是 mkv 而失败了，则有可能您的 mkv 视频没有字幕流，画面中的字幕是烧到画面中的。</source>
        <translation>The subtitle format only supports srt, ass, or mkv contains subtitle stream. Your current subtitle file is not supported. </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4962"/>
        <source>创建输出文件夹失败，可能是已经创建上了
</source>
        <translation>Failed to create output folder, maybe it already exists. </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4965"/>
        <source>总共有 %s 段要处理，现在开始导出第 %s 段……
</source>
        <translation>We got %s clips to process, now exporting number %s. </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="5020"/>
        <source>导出完成
</source>
        <translation>Export complete</translation>
    </message>
</context>
<context>
    <name>SystemTray</name>
    <message>
        <location filename="../QuickCut.py" line="165"/>
        <source>退出</source>
        <translation>Exit</translation>
    </message>
</context>
<context>
    <name>TencentTrans</name>
    <message>
        <location filename="../QuickCut.py" line="6345"/>
        <source>即将识别：</source>
        <translation>About to recognize: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="6393"/>
        <source>服务器有点错误,错误原因是：</source>
        <translation>Server error, reason: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="6396"/>
        <source>云端任务排队中，10秒之后再次查询
</source>
        <translation>Cloud mission is queeing, require again after 10 seconds. </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="6399"/>
        <source>任务进行中，3秒之后再次查询
</source>
        <translation>Mission is under work, require again after 3 seconds. </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="6430"/>
        <source>
上传目标路径：</source>
        <translation>Upload destination: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="6433"/>
        <source>上传音频中
</source>
        <translation>Uploading audio</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="6435"/>
        <source>音频上传完毕，路径是：%s
</source>
        <translation>Audio uploaded, the url is: %s</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="6437"/>
        <source>正在识别中
</source>
        <translation>Recognizing</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="6441"/>
        <source>正在读取结果中
</source>
        <translation>Reading the result</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="6504"/>
        <source>现在开始生成单声道、 16000Hz 的 wav 音频：
</source>
        <translation>Now generate monochannel, 16000Hz wav audio file: </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="6517"/>
        <source>已删除 oss 音频文件
</source>
        <translation>OSS audio file deleted successfully! </translation>
    </message>
</context>
<context>
    <name>VoiceInputMethodTranscribeSubtitleWindow</name>
    <message>
        <location filename="../QuickCut.py" line="4614"/>
        <source>语音输入法转写字幕工作窗口</source>
        <translation>VoiceInputMethod transcribe subtitle window</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4622"/>
        <source>暂停</source>
        <translation>Pause</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4624"/>
        <source>继续</source>
        <translation>Continue</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4653"/>
        <source>正在生成 wav 文件
</source>
        <translation>Generating wav file...</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4665"/>
        <source>检测到已存在同名字幕文件，已有 %s 条字幕，将会自动载入到下面的编辑框
</source>
        <translation>A subtitle with same name already exists, it has %s subtitles, they will be automaticlly loaded to the box below
</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4666"/>
        <source>如果不希望接着已有内容做字幕，请手动删除已存在的字幕文件
</source>
        <translation>If you don&apos;t want to append to the existing subtitles, you may delete the content in the box below. </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4702"/>
        <source>第 %s 句识别完毕！
</source>
        <translation>Number %s clip is recognized successfully! </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4704"/>
        <source>片段识别结果是空白，有可能音频设置有误，请查看视频教程：https://www.bilibili.com/video/BV1wT4y177kD/
</source>
        <translation>The result is blank, please make sure you&apos;ve watched the video tutorial: https://www.bilibili.com/video/BV1wT4y177kD/</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4725"/>
        <source>已得到 wav 文件，并分段，共有 %s 段
</source>
        <translation>wav file has been generated and sliced to %s clips. </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4726"/>
        <source>该功能需要设置电脑一番，所以请确保已看过视频教程：
</source>
        <translation>This function requires some pre-work in your computer, make sure you&apos;ve watched the video tutorial: 
</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4727"/>
        <source>关闭本页面后，下方输入框的内容会自动保存到 %s 中
</source>
        <translation>After closing this page, the contents below will be saved to %s automaticlly. 
</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4729"/>
        <source>现在按下 继续 键开始听写音频
</source>
        <translation>Now hit &quot;Continue&quot; button to transcribe audio. </translation>
    </message>
</context>
<context>
    <name>YouGetYoutubeDlInstallThread</name>
    <message>
        <location filename="../QuickCut.py" line="4824"/>
        <source>开始执行命令
</source>
        <translation>Command started. </translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4840"/>
        <source>安装 you-get 和 youtube-dl 失败了。安装教程请看：https://www.bilibili.com/video/BV18T4y1E7FF?p=5</source>
        <translation>Failed to install you-get and youtue-dl. Please watch video tutorial: https://www.bilibili.com/video/BV18T4y1E7FF?p=5</translation>
    </message>
    <message>
        <location filename="../QuickCut.py" line="4841"/>
        <source>
命令执行完毕
</source>
        <translation>Command complete. </translation>
    </message>
</context>
</TS>
