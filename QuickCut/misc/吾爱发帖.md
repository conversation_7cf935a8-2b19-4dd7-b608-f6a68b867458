我是作者。先将最重要的信息写在前面：

Quick Cut 开源的，你可以免费使用它，但正因为开源，因此插入恶意代码是很容易的事，所以请认准仓库发行页面的下载地址：

- 仓库地址：[Gitee](https://gitee.com/haujet/QuickCut) 和 [GitHub](https://github.com/HaujetZhao/QuickCut) 
- 发行版发布地址：[Gitee releases](https://gitee.com/haujet/QuickCut/releases) 和 [Github releases](https://github.com/HaujetZhao/QuickCut/releases) 
- Win64 绿色版下载地址：https://wwe.lanzous.com/b015n2n5a 密码:f4t3
- Python pip 安装：`pip install Quick-Cut`

> Quick Cut 是用 Python 写的，所以我将 Quick Cut 也发布在了 [pypi](https://pypi.org/project/Quick-Cut/) 库，只要你装了 python，使用 `pip install Quick-Cut` 就可以安装上 Quick Cut，不过 pypi 库中的版本没有自带 FFmpeg，需要你手动安装。（都会用 pip 安装东西了，相信安装  FFmpeg 就不用教了吧）
>
> 我没有 Win32 、MacOS、Linux 系统的电脑，所以这些系统打包不了。但你们可以通过 pip 方法安装。
>
> Windows 系统用 pip 安装时，可能会遇到 pyaudio 的安装问题，可以参照 [这篇文章](https://blog.csdn.net/Sau_Hit/article/details/85938063) 解决。

另外，每一个页面我都做了 [视频教程](https://www.bilibili.com/video/BV18T4y1E7FF)，基本所有的问题看了视频都能解决，在主页面的预设列表下方有 `查看预设帮助` 按钮，有关于压制方面的帮助，可以解决你关于转码清晰不清晰的问题，也瞅瞅，所以不要连官方解答都不看，就无脑提问。

# <img src="https://gitee.com/haujet/QuickCut/raw/master/QuickCut/icon.ico" alt="icon.ico" style="zoom:25%;" /> Quick Cut

**Quick Cut** 是一款轻量、强大、好用的视频处理软件。它是一个轻量的工具，而不是像 Davinci Resolve、Adobe Premiere 那样专业的、复杂的庞然大物。Quick Cut 可以满足普通人一般的视频处理需求：压缩视频、转码视频、倒放视频、合并片段、根据字幕裁切片段、自动配字幕、自动剪辑……

它是开源的，你可以免费使用它。

Gitee 地址：https://gitee.com/haujet/QuickCut

GitHub 地址：https://github.com/HaujetZhao/QuickCut

界面预览：

<img src="https://gitee.com/haujet/QuickCut/raw/master/assets/image-20200726203040942.png" alt="image-20200726203040942" style="zoom:50%;" />

## 📝 背景

好几年前，有一次想将一个视频中的片段剪出来，才发现，市面上根本没有给普通用户用的视频处理软件。

我去百度、知乎上搜【视频剪辑软件】，陆续得到了以下结果：

- **Adobe Premiere**，正版一年上千元、随时【停止运行】的专业剪辑软件
- **Vegas** 等专业软件就不再列了
- **爱剪辑**，一个导出就带推广片头的中文剪辑软件
- **格式工厂**，一个老牌的国产转码编辑软件
- **小丸工具箱**，一个从 Bilibili 知道的压制软件
- 还有大大小小的其它软件

我就只想剪一小个片段啊！专业软件学不起、买不起、电脑太卡带不起！可是其它小软件、许多国产剪辑软件，都有这一些问题：

- 国外软件选项太多，各种参数看不懂。
- 多数免费软件都带水印，或者导出后画质会下降，要么导出的体积巨大，耗时长不说，还不是无损剪辑。

用的最好的还是 **格式工厂** 和 **小丸工具箱** 。但他们都功能很少，还有小丸工具箱，官网的下载地址是百度网盘链接已经挂了，也不知道从第三方下载的版本有没有木马、广告……

后来，从视频压制这个话题，我知道了 **FFmpeg** 这个神级开源工具，它的功能之强大、应用之广泛到惊掉了我的下巴！但它是个命令行工具，到百度上一搜，也只是些文章说怎样怎样的命令可以做什么、常用命令有什么。相关的图形界面工具，一个好用的都没有！（当然要点名表扬下 Lossless Cut，它还是很好用的，只是功能太少）。没有一个软件能让 FFmpeg 在普通大众用户手中发挥潜力，真是可惜了！

于是一通操作，把 FFmpeg 的官方文档逐句做了翻译，记了笔记，然后用它来处理视频，真是畅快！免费、无广告，剪出来的视频也可以画质无损。100 兆的视频压缩到 10 兆，肉眼画质无损，那是一个巴适！

但是有一个问题！每次要处理一个视频，都要手动输入命令行，非常的麻烦，有时候忘记执行某个操作是用哪个参数了，还要去翻笔记，难受！于是我就想做一个图形界面工具，想要做什么，在里面点两下，命令参数啥的都是自动生成，最后点击运行就好。于是先后做了基于 **Tasker** 的安卓端 FFmpeg GUI Tool、基于 **Quicker** 的 FFmpeg GUI Tool。

但是上面两个平台都局限太多，想要好用，还是得自己做 GUI。我自己只是入门 python 的水平，只能硬着头皮花了几天在学习平台 B 站看完了 PyQt 的入门。然后边查边做，因为 PyQt 的注释很少，有时一个简单的小细节要花半天到一天处理。最后，成品还是出来了！

当然，除了 **FFmpeg** 的功能外，我还做了 **语音自动转字幕** 功能。这里，我要不指名地 Diss 一些商家：

- 首先是价格，贵的要 1元/分钟，便宜些的也要 4毛/分钟！转个 20 分钟的视频，就要收我 8 元，抢钱呐！啊？你们是没有语音引擎的，都是用的 API，大批量采购，你们用的 API 价格也就 1.2 元/小时 左右吧！用 API 成本几十倍的价格提供服务，这吃相，真是难看。
- 然后是上传视频。语音转字幕，上传音频便足够了，一些商家却需要上传完整视频！2GB 的视频，通常其音频只有几十 MB 左右。你们是觉得：用户见转写了几十 MB 的视频，会觉得这么小的文件，1元/分钟的价格不值，转写了 2GB 的视频，一看这转写体积，就觉得花钱花的值？

在 Quick Cut 里，我做了 **语音自动转字幕** 功能，可以使用阿里或腾讯的 Api。就以阿里的 API 为例，普通元套餐的商用价格是 2.5 元/小时，四十五几乎就是不要钱！下面的帮助里，我写下了申请阿里 API 的教程，只要填入 API，就可以使用语音转字幕了。

另外，我还将另一个 **自动剪辑神器** 放进了 Quick Cut，将你的 Vlog、视频教程一键自动剪好。下面会有详细介绍。

同时，推荐 Quick Cut 中一个对学习外语的同学非常有用的功能：**将字幕中的每一句话对应的视频剪成片段提取出来**。用于制作外语学习的视频素材爽得不要不要的！应该算是 **Anki** 用户的一个福利功能吧！

## ✨ 特性

- 简单的界面
- FFmpeg 预设丰富
- 可自定义预设
- 合并视频片段
- 逐句提取每句字幕对应的视频片段
- 自动转字幕
- 自动剪辑
- ……更多待探索

## 🔮 界面和功能介绍

### FFmpeg 界面

在这个界面，你可以使用许多 ffmpeg 预设，对音视频进行处理，比如说：

我想将手机上录制的视频压制下，减小它的大小，那么只需要：先在 **输入1** 框输入待压制的视频文件（此时会自动生成输出文件名），再在右侧预设列表选择 **H264压制** 预设（此时会自动生成总命令），最后点击底部的 **运行** 按钮，就会启动压制了。

**还有啊，转格式是基本操作！输出框的后缀名填什么，就会输出对应的格式！**

[点击去查看 Quick Cut 压制的视频教程](https://www.bilibili.com/video/BV18T4y1E7FF?p=1)

[点击去查看 Quick Cut 预设讲解视频教程](https://www.bilibili.com/video/BV18T4y1E7FF?p=2)

### 分割视频界面

这个界面有三个功能：根据字幕分割视频、根据大小分割视频、根据时长分割视频。

根据字幕分割视频是个神级功能，尤其适合于制作外语学习的视频素材。将例如美剧的视频放进去，再把相应的字幕文件放进去，就可以将每一句字幕对应的视频片段剪出来！如果你的字幕时间轴和视频时间轴有偏差，还可以进行手动的校准。

根据时间分割视频和根据大小分割视频主要是针对分享到短视频平台和微信平台的。

[点击去查看 Quick Cut 分割视频的视频教程](https://www.bilibili.com/video/BV18T4y1E7FF?p=3)

<img src="https://gitee.com/haujet/QuickCut/raw/master/assets/image-20200725103345859.png" alt="image-20200725103345859" style="zoom:50%;" />

### 合并片段界面

这个简单，要合并的视频拖进去，调整下顺序，点击运行，就可以将这些视频合并成一个文件。从 ig 下载的 15 秒 story 视频片段就可以轻松合并啦！

[点击去查看 Quick Cut 合并片段的视频教程](https://www.bilibili.com/video/BV18T4y1E7FF?p=4)

<img src="https://gitee.com/haujet/QuickCut/raw/master/assets/image-20200725103322509.png" alt="image-20200725103322509" style="zoom:50%;" />

### 下载视频界面

这个界面提供了两个命令行工具的图形界面用于下载视频，最简单的用法就是将链接复制进去，然后点击下载。支持的网站有很多比如优酷、B站、YouTube、P站（逃）……

另外你还可以在里面设置cookies，就能够用你大会员身份登录的 cookie 信息下载大会员视频画质了。

[点击去查看 Quick Cut 下载视频的视频教程](https://www.bilibili.com/video/BV18T4y1E7FF?p=5)

<img src="https://gitee.com/haujet/QuickCut/raw/master/assets/image-20200725103257140.png" alt="image-20200725103257140" style="zoom:50%;" />

### 自动剪辑界面

自动剪辑的原理是通过给视频中有声音的片段和没有声音的片段施加不同的播放速度，达到只保留有关键信息部分的效果，非常适合做vlog和视频教程。

同时你也可以选择使用阿里云或者腾讯云的语音服务，先将视频转出字幕之后，再根据字幕中的关键词对视频片段进行保留和删除操作。

除了下面那个演示视频，在本教程中的所有视频，都使用了自动剪辑，然后才上传的。

[点击去查看 Quick Cut 自动剪辑的效果演示视频](https://www.bilibili.com/video/BV18T4y1E7FF?p=6)

[点击去查看 Quick Cut 自动剪辑的视频教程](https://www.bilibili.com/video/BV18T4y1E7FF?p=7)

<img src="https://gitee.com/haujet/QuickCut/raw/master/assets/image-20200725103228908.png" alt="image-20200725103228908" style="zoom:50%;" />

### 自动转字幕界面

只要将你的视频或者音频文件拖进去，然后点击运行，就可以生成一个srt格式的字幕。

语音识别方面使用了阿里云或者腾讯云的引擎，准确率有95%以上。如果想给自己的视频配字幕，就可以先用这个功能，自动转出字幕之后，再手动修改一下里边偶尔的错别字，效率非常高。

语音识别引擎需要用户自己去阿里云官网申请 API 才能用（对申请过程我做了[视频教程](https://www.bilibili.com/video/BV18T4y1E7FF?p=11)）。阿里云的语音服务开通后，每个新用户有3个月的免费试用时间，在这3个月内，每天都有两小时的录音文件转换额度。试用期过后，商业版的价格是每小时音频转换2.5元，随着使用量的增加，这个价格还会更低。如果买1000小时的套餐，价格可以低到1.5元每小时。

如果你对比一下目前网上能找到的视频转字幕服务，你就会知道 Quick Cut 的这个转字幕功能有多便宜：

- 网易见外工作台，普通的视频转字幕，收费1元每分钟，60元每小时。英文转字幕价格翻倍。
- 号称全网最低价的突字幕，收费0.005元每秒，3毛每分钟，18元每小时。
- Arctime 收费30积分每分钟，也就是3毛每分钟，18元每小时。

来对比一下：阿里云 2.5 元每小时，前三个月每天免费用两小时，用得越多价，格还会更低。

腾讯云方面的价格还会更低，只是转换速度没有阿里云快。所以推荐使用阿里云。就算是用商业版每小时2.5元的价格也不心疼。

如果你急着把最新的美剧视频下载下来，字幕组还没有出字幕，但是生肉太难啃，就可以用这个转字幕功能，将英文字幕转出来，配合着看。

如果你是视频工作者、UP主、视频公司负责人，平常有大量的视频需要转换字幕，用这个就可以舍去使用其他网上平台导致的每小时几十元的成本，一个月下来成本或许能节约到上千元。。

哎，说实话，要是这软件推广开来，对一些视频转字幕的服务商，真的是断人财路，杀人父母。

[点击去查看 Quick Cut 自动转字幕的演示](https://www.bilibili.com/video/BV18T4y1E7FF?p=8)

<img src="https://gitee.com/haujet/QuickCut/raw/master/assets/image-20200725103137457.png" alt="image-20200725103137457" style="zoom:50%;" />

### 语音识别输入界面

如果你已经添加上阿里云的语音引擎，就可以使用阿里云的一句话识别服务进行语音识别输入。在任意界面，只要长按键盘上的大写锁定键超过0.3秒钟，就可以开始说话，进行语音识别。松开按键之后，识别结果会自动打到输入框。

有人推荐使用讯飞的语音识别，但是讯飞语音识别的快捷键在 F6 那个位置，离主键盘的位置非常远，手指够过去费劲；而且那里有很多大小一样的按键，这个快捷键也不好找；他还会和很多软件的快捷键冲突。

而长按大写锁定键进行语音识别就非常方便，离打字的手非常近，需要的时候很轻松就能按到，也不会有快捷键冲突。

阿里云语音识别的准确率非常高，这篇文章大部分都是用这个语音识别功能打的，然后修改了少量错字。

[点击去查看 Quick Cut 语音识别输入的演示](https://www.bilibili.com/video/BV18T4y1E7FF?p=9)

<img src="https://gitee.com/haujet/QuickCut/raw/master/assets/image-20200725103107328.png" alt="image-20200725103107328" style="zoom:50%;" />

### 设置界面

在设置界面你就可以在这里配置语音识别的引擎。

勾选上 **点击关闭按钮时隐藏到托盘** ，就可以让软件常驻到后台，方便快捷的调出来剪辑视频、语音输入。Quick Cut 的资源占用量非常少，可以放心地放到后台，不影响性能。

[点击去查看 Quick Cut 配置阿里云语音识别引擎的视频教程](https://www.bilibili.com/video/BV18T4y1E7FF?p=9)

<img src="https://gitee.com/haujet/QuickCut/raw/master/assets/image-20200725103013287.png" alt="image-20200725103013287" style="zoom:50%;" />

### 帮助界面

在帮助界面里，有如下按钮，见名知意：

<img src="https://gitee.com/haujet/QuickCut/raw/master/assets/image-20200725102850979.png" alt="image-20200725102850979" style="zoom:50%;" />

## 🔨 开发

这一节普通用户就不用看了。软件是使用 Python 进行开发的，如果你想从源码运行，可以参考仓库的 README 页面的开发一节。



## 😀 交流

如果有软件方面的反馈可以提交 issues 

## 🙏 鸣谢

感谢知乎上的 @[Python与模具](https://www.zhihu.com/people/xuhui112-ben) 的帮助，让我得以用 nuitka 对软件进行打包。