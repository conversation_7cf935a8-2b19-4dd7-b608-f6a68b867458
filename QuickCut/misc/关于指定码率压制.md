控制视频大小有两种方法：

- 恒定画面质量，可变码率。也就是 crf 方式

  这时，编码器会根据你要求的画面质量，自动分配码率，给复杂的画面部分多分配点码率，给简单的画面少分配点码率，可以得到画面质量均一的输出视频，这是最推荐的压制方式。不过无法准确预测输出文件的大小。假如你的视频全程都是非常复杂、包含大量背景运动的画面，那么可能压制出来的视频，比原视频还要大。这里的压制方式用的就是 恒定画面质量 的方式。

- 恒定码率

  这时，编码器会根据你的要求，给每一秒都分配相同的码率，可以准确预测输出文件的大小。但是，由于码率恒定，可能有些复杂的片段，你分配的码率不够用，就会画质下降，有些静态部分多的画面，就浪费了很多码率，所以一般不推荐用。

这个预设就是使用恒定码率的方法进行压制，-b:a 256k 表示音频码率为 256kb，也就是音频每秒钟会占 $256 / 8 = 42kB$ 大小， -b:v 6000k 表示视频码率为 6000kb，也就是视频每秒钟会占 $6000 / 8 = 750kB$ 大小。这里没有使用 -c:v 指定视频编码器，此时编码器默认是 libx264，没有用 -c:a 指定音频编码器，则默认用 aac 音频编码器。

 **以上所有参数你都可以手工修改，以达到需要的视频质量和体积**