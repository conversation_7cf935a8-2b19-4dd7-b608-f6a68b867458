控制视频大小有两种方法：

- 恒定画面质量，可变码率。也就是 crf 方式

  这时，编码器会根据你要求的画面质量，自动分配码率，给复杂的画面部分多分配点码率，给简单的画面少分配点码率，可以得到画面质量均一的输出视频，这是最推荐的压制方式。不过无法准确预测输出文件的大小。假如你的视频全程都是非常复杂、包含大量背景运动的画面，那么可能压制出来的视频，比原视频还要大。这里的压制方式用的就是 恒定画面质量 的方式。

- 恒定码率

  这时，编码器会根据你的要求，给每一秒都分配相同的码率，可以准确预测输出文件的大小。但是，由于码率恒定，可能有些复杂的片段，你分配的码率不够用，就会画质下降，有些静态部分多的画面，就浪费了很多码率，所以一般不推荐用。

这个预设就是使用恒定码率的方法进行压制，但是使用的是二压，也是就 2-pass 的方法。

-b:a 256k 表示音频码率为 256kb，也就是音频每秒钟会占 $256 / 8 = 42kB$ 大小，没有用 -c:a 指定音频编码器，则默认用 aac 音频编码器。

 -b:v 2000k 表示视频码率为 2000kb，也就是视频每秒钟会占 $2000 / 8 = 250kB$ 大小，使用 -c:v libx264 指定了视频编码器 libx264，没有用 -c:a 指定音频编码器，则默认用 aac 音频编码器。

在这个预设中，FFmpeg 会先进行一轮压制，但不输出任何视频，而是输出一个文本文件，描述了视频里哪些时间画面比较复杂，需要较多的码率，哪些时间画面比较简单需，需要的码率不多，然后第二次压制的时候，就会根据这个文本的码率信息，将马路进行合理分配，使最后的平均码率达到目标码率。

 **以上所有参数你都可以手工修改，以达到需要的视频质量和体积**