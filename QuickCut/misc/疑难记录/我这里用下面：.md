我这里用下面：

```python
import io
wrapper = io.TextIOWrapper(self.process.stdout, encoding='utf-8')
for line in wrapper:
	print(line)
```

时:

```
ffmpeg -y -hide_banner -i "C:/Users/<USER>/Videos/QuickCut打包问题解决指南.mp4" "C:/Users/<USER>/Videos/QuickCut打包问题解决指南_out.mp4"
```

可以正常打印，但：

```
echo 中文
```

就提示 utf-8 无法解析字符。。。。

如果我用：

```python
import io
wrapper = io.TextIOWrapper(self.process.stdout, encoding='gbk')
for line in wrapper:
	print(line)
```

情况就反过来了。。。