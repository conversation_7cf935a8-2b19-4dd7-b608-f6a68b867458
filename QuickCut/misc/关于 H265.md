#### H265压制视频

输入文件一，模板中选择 H265 压制，输出选项会自动设置好，点击 Run ，粘贴编码，等待压制完成即可。

 

#### 选项帮助：

##### 输出文件选项：

-c:v 设置视频编码器，-c:v libx265 表示使用 libx265 编码器输出 h265 编码的视频

-crf 恒定视频质量的参数，-crf 28 表示输出视频质量为 crf 28 。

-c:a 设置音频编码器，-c:a copy 表示复制音频流，不做任何重新编码。你也可以改成：-c:a aac，-c:a mp3

 **以上所有参数你都可以手工修改，以达到需要的视频质量和体积**

 

#### 注意事项

注意，压制视频的话，输入文件放一个就行了哈，别放两个输入，FFmpeg 会自动把最高分辨率的视频流和声道数最多的音频流合并输出的。

 

#### 相关科普

压制过程中你可以从命令行看到实时压制速度、总码率、体积、压制到视频几分几秒了。

相关解释：H264是一个很成熟的视频编码格式，兼容性也很好，一般你所见到的视频多数都是这个编码，小白压制视频无脑选 H264 就行了。而 H265 是一个比较新的编码，压缩率高，但由于授权费较贵，目前支持的设备不多，兼容性差，但如果对体积压制有较大的要求，可以用 H265 进行压制。

这个参数下，画质和体积能得到较好的平衡，一般能把手机相机拍摄的视频压制到原来体积的1/5左右，甚至更小，画质也没有明显的损失。

但是注意，**自己录制的视频**压缩率很低，压制后会有很好的效果，从**视频网站下载的**电影、电视剧就**不要再压制**了，人家一集视频，都是已经用**性能拔尖的服务器**使用**最费时**、**压缩率最高**的参数压制了**几小时甚至几天**的，压缩率已经到了极致了，再让你用手上的设备解码重新压制一下，体积可能**不减反增**。

控制视频大小有两种方法：

- 恒定画面质量，可变码率。也就是 crf 方式

  这时，编码器会根据你要求的画面质量，自动分配码率，给复杂的画面部分多分配点码率，给简单的画面少分配点码率，可以得到画面质量均一的输出视频，这是最推荐的压制方式。不过无法准确预测输出文件的大小。假如你的视频全程都是非常复杂、包含大量背景运动的画面，那么可能压制出来的视频，比原视频还要大。这里的压制方式用的就是 恒定画面质量 的方式。

- 恒定码率

  这时，编码器会根据你的要求，给每一秒都分配相同的码率，可以准确预测输出文件的大小。但是，由于码率恒定，可能有些复杂的片段，你分配的码率不够用，就会画质下降，有些静态部分多的画面，就浪费了很多码率，所以一般不推荐用。如果你想用这个方案，请参阅 [控制码率压制视频](#控制码率压制视频) 

此处输出选项里的 -crf 28 是画质控制参数。越小画质越高，同时体积越大。 0 代表无损画质，体积超大。H265 编码效率比较高，-crf 28 就可以达到 H264 的 crf 23 相同的画质，同时缩小一半的体积。

