<!doctype html>
<html>
<head>
<meta charset='UTF-8'><meta name='viewport' content='width=device-width initial-scale=1'>
<title>关于使用硬件加速</title></head>
<body><p>关于使用硬件加速：</p>
<p>目前硬件加速支持两种编码格式：H264 和 H265</p>
<p>有3种加速方法，分别对应三家的硬件：Inter、AMD、Nvidia</p>
<p>不过在苹果电脑上，不管你用的哪家的硬件，都是使用 videotoolbox 编码器。</p>
<p>需要注意的是，即便你的电脑拥有 Nvidia 显卡，可能也用不了 Nvidia 的硬件加速编码，因为 Nvidia 硬件加速依赖于显卡内部的一种特定的 GPU 的物理部分，专用于编码。只有在 GTX10 和 RTX20 以上的显卡才搭载有这个物理部分。</p>
<p>使用硬件编码器进行编码，只需要将输出选项中的编码器改成硬件编码器即可，其中：</p>
<ul>
<li><code>-c:v h264_qsv</code> 对应 Intel H264 编码</li>
<li><code>-c:v h264_amf</code> 对应 AMD H264 编码</li>
<li><code>-c:v h264_nvenc</code> 对应 Nvidia H264 编码</li>
<li><code>-c:v h264_videotoolbox</code> 对应苹果电脑的 H264 编码</li>
<li><code>-c:v hevc_qsv</code> 对应 Intel H265 编码</li>
<li><code>-c:v hevc_amf</code> 对应 AMDH265 编码</li>
<li><code>-c:v hevc_nvenc</code> 对应 Nvidia H265 编码</li>
<li><code>-c:v hevc_videotoolbox</code> 对应苹果电脑的 H265 编码</li>

</ul>
<p><code>-c:v</code> 表示视频（Video）的编码器（codec）</p>
<p>在使用硬件加速编码器的时候，控制输出视频的质量是使用 <code>qscale</code> 参数，他的数值可以从 <code>0.1 - 255</code> 不等，数值越小，画质越高，码率越大，输出文件体积越大。同一个数值对于不同的编码器画质的影响效果不同。所以你需要自己测试，在玛律大小和视频画质之间找到一个平衡的 <code>qscale</code> 数值。</p>
<p>目前所有的硬件加速选项都是类似这样的：<code>-c:v h264_qsv -qscale 15</code> ，这表示使用英特尔 h264 硬件加速编码器，视频质量参数为15。你可以更改里面的数值，以达到你期望的画质效果。</p>
</body>
</html>