**Quick Cut v1.6.1** 新增了自定义主题。在软件的根目录有一个 `style.css` 文件，在里面可以更改软件界面的样式。在主界面按下 `F5` 就可以刷新样式。所以，用户可以在更改 `style.css` 后，在主界面按下 `F5` ，立马看到实际效果。 

Quick Cut 使用了 Qt 做的图形界面，其样式可以通过一个叫 QSS 的样式表控制，其实也就是一张 css 表。

### QssStylesheetEditor

GitHub 上有一个开源工具，可以方便地制作 QSS 表：[QssStylesheetEditor](https://github.com/hustlei/QssStylesheetEditor/) 

这里是它的中文文档：[简体中文](https://github.com/hustlei/QssStylesheetEditor/blob/master/readme_zh-CN.md) 

Windows 端只要下载，运行就可以用了，其它端只要下载 `whl` 文件，用 `pip install` 安装就可以用了。

### QUI

[QSS-Skin-Builder](https://github.com/satchelwu/QSS-Skin-Builder) 这也是一个开源工具。