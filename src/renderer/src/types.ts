export type DataType = 'size' | 'frame'

export enum MainProcessNoticeType {
	END = 'end',
	PROGRESS = 'progress',
	ERROR = 'error',
	DIREDCTORY_CHECK = 'directoryCheck',
	STOP = 'stop',
	FILE_IS_EXISTS = 'file_is_exists',
	START = 'start',
	SUCCESS = 'success'
}

//视频状态
export enum VideoState {
	READAY = 'ready',
	COMPRESS = 'compress',
	ERROR = 'error',
	FINISH = 'finish'
}

export type VideoType = {
	name: string
	path: string
	progress: number
	state: VideoState
}

//视频压缩参数类型
export type CompressOptions = {
	file: VideoType
	fps: number
	size: string
	videoBitrate: number
	saveDirectory: string
}

// 音频提取参数类型
export type ExtractAudioOptions = {
	inputPath: string
	outputPath?: string
	format: 'mp3' | 'aac' | 'wav' | 'flac' | 'ogg'
	bitrate?: string // 例如 '128k', '192k', '320k'
	sampleRate?: string // 例如 '44100', '48000'
	channels?: number // 1 或 2
}

// 视频格式转换参数类型
export type VideoConvertOptions = {
	inputPath: string
	outputPath?: string
	format: string
	videoBitrate?: string
	audioBitrate?: string
	width?: number
	height?: number
	deleteOriginal?: boolean // 转换完成后是否删除原视频
}

// 视频压缩参数类型（新版）
export type VideoCompressOptions = {
	inputPath: string
	outputPath?: string
	videoBitrate: string
	audioBitrate?: string
	width?: number
	height?: number
	fps?: number
	preset?: string
	deleteOriginal?: boolean // 压缩完成后是否删除原视频
	saveDirectory?: string // 保存目录（用于批量处理）
}

// 批量视频压缩参数类型
export type BatchVideoCompressOptions = Omit<VideoCompressOptions, 'inputPath' | 'outputPath'> & {
	saveDirectory?: string
}

// 视频截帧参数类型
export type ExtractFramesOptions = {
	inputPath: string
	outputDir: string
	interval: number // 间隔秒数
	format?: string // 输出图片格式，默认为 jpg
	quality?: number // 图片质量 (1-100)，默认为 80
}

// 图片压缩参数类型
export type ImageCompressOptions = {
	inputPath: string
	outputPath?: string
	quality: number // 1-100
	width?: number
	height?: number
	format?: string // 输出格式，默认与输入相同
	deleteOriginal?: boolean // 压缩完成后是否删除原图片
}

// 图片格式转换参数类型
export type ImageConvertOptions = {
	inputPath: string
	outputPath?: string
	format: 'jpeg' | 'png' | 'webp' | 'avif' | 'tiff' | 'gif'
	quality?: number // 1-100
}

// 图片清晰度提升参数类型
export type ImageEnhanceOptions = {
	inputPath: string
	outputPath?: string
	scale: number // 放大倍数，例如 2 表示放大 2 倍
	sharpness: number // 锐化程度 (0-100)
	denoise: boolean // 是否降噪
	denoiseLevel?: number // 降噪级别 (0-100)
	format?: string // 输出格式，默认与输入相同
	quality?: number // 输出质量 (1-100)
	deleteOriginal?: boolean // 处理完成后是否删除原图片
}
